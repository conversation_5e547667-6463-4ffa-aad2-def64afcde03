from exchangelib import Credentials, Account, DELEGATE, Message, Mailbox, Folder, Configuration, NTLM, FaultTolerance
from exchangelib.protocol import BaseProtocol, NoVerifyHTTPAdapter
from exchangelib.errors import Unauthorized<PERSON>rror, TransportError, RateLimitError, ErrorServerBusy
import os
import logging
import time
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exchange_client.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def configure_account(username, password, email, disable_ssl_verification=False):
    """Configure Exchange account with improved error handling and connection pooling."""
    logger.info(f"Configuring account for {email}")

    # Disable SSL certificate verification if requested
    if disable_ssl_verification:
        BaseProtocol.HTTP_ADAPTER_CLS = NoVerifyHTTPAdapter
        logger.warning("SSL certificate verification disabled")

    credentials = Credentials(username=username, password=password)

    # Try autodiscovery first
    try:
        logger.info("Attempting autodiscovery...")
        account = Account(
            primary_smtp_address=email,
            credentials=credentials,
            autodiscover=True,
            access_type=DELEGATE
        )
        logger.info("Autodiscovery successful")
        return account
    except (UnauthorizedError, TransportError) as e:
        logger.warning(f"Autodiscovery failed: {e}")
        logger.info("Trying manual configuration...")

        # Fallback to manual configuration with improved settings
        config = Configuration(
            server='owa-intern.telekom.de',
            credentials=credentials,
            auth_type=NTLM,
            retry_policy=FaultTolerance(max_wait=120),  # Increased timeout for rate limiting
            max_connections=3  # Connection pooling
        )
        account = Account(
            primary_smtp_address=email,
            config=config,
            autodiscover=False,
            access_type=DELEGATE
        )
        logger.info("Manual configuration successful")
        return account

def read_emails_with_retry(account, folder_name='inbox', limit=10, max_retries=3):
    """Read emails with improved retry logic and error handling."""
    logger.info(f"Reading {limit} emails from {folder_name}")

    for attempt in range(max_retries):
        try:
            folder = getattr(account, folder_name)
            emails = folder.all().order_by('-datetime_received')[:limit]

            logger.info(f"Successfully retrieved {len(list(emails))} emails")

            # Reset the query since we consumed the iterator above
            emails = folder.all().order_by('-datetime_received')[:limit]
            for i, item in enumerate(emails, 1):
                logger.info(f"Processing email {i}/{limit}")
                print(f"Subject: {item.subject}")
                print(f"Sender: {item.sender}")
                print(f"Received: {item.datetime_received}")
                print("-" * 50)
            return True

        except RateLimitError as e:
            logger.warning(f"Rate limit hit on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                wait_time = min(60, 10 * (2 ** attempt))  # Exponential backoff, max 60s
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                logger.error("Max retries reached due to rate limiting")
                raise

        except ErrorServerBusy as e:
            logger.warning(f"Server busy on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                wait_time = min(30, 5 * (2 ** attempt))  # Shorter backoff for server busy
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                logger.error("Max retries reached due to server being busy")
                raise

        except Exception as e:
            logger.error(f"Unexpected error on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                logger.info("Retrying in 5 seconds...")
                time.sleep(5)
            else:
                logger.error("Max retries reached")
                raise

    return False

def move_email(account, subject, new_folder_name):
    email_to_move = account.inbox.filter(subject=subject)[0]
    new_folder = getattr(account, new_folder_name)
    email_to_move.move(to_folder=new_folder)

def delete_email(account, subject):
    email_to_delete = account.inbox.filter(subject=subject)[0]
    email_to_delete.delete()

def filter_emails(account, folder_name='inbox', search_term=''):
    folder = getattr(account, folder_name)
    filtered_emails = folder.filter(subject__contains=search_term)
    for email in filtered_emails:
        print(f"Subject: {email.subject}")
        print(f"Sender: {email.sender}")
        print(f"Body: {email.body}")
        print(f"Received: {email.datetime_received}")
        print("-" * 80)

def create_folder(account, folder_name, parent_folder_name='root'):
    parent_folder = getattr(account, parent_folder_name)
    new_folder = Folder(parent=parent_folder, name=folder_name)
    new_folder.save()

def rename_folder(account, old_folder_name, new_folder_name):
    folder = getattr(account, old_folder_name)
    folder.name = new_folder_name
    folder.save()

def delete_folder(account, folder_name):
    folder = getattr(account, folder_name)
    folder.delete()

def send_email(account, subject, body, to_recipients):
    new_email = Message(
        account=account,
        subject=subject,
        body=body,
        to_recipients=[Mailbox(email_address=recipient) for recipient in to_recipients]
    )
    new_email.send()

def main():
    """Main function with improved error handling and logging."""
    # Load environment variables from .env file with explicit path and override
    env_path = os.path.join(os.getcwd(), '.env')
    load_dotenv(dotenv_path=env_path, override=True)

    # Get credentials from environment variables
    username = os.getenv('USERNAME')
    password = os.getenv('PASSWORD')
    email = os.getenv('EMAIL')
    # Get SSL verification setting (defaults to true for corporate environments)
    disable_ssl_verification = os.getenv('DISABLE_SSL_VERIFICATION', 'true').lower() in ('true', '1', 'yes', 'on')

    # Check if all required environment variables are set
    if not all([username, password, email]):
        missing_vars = []
        if not username:
            missing_vars.append('USERNAME')
        if not password:
            missing_vars.append('PASSWORD')
        if not email:
            missing_vars.append('EMAIL')

        error_msg = f"Missing required environment variables: {', '.join(missing_vars)}. Please check your .env file."
        logger.error(error_msg)
        raise ValueError(error_msg)

    logger.info(f"Starting Exchange client for {email}")
    logger.info(f"SSL verification disabled: {disable_ssl_verification}")

    try:
        account = configure_account(username, password, email, disable_ssl_verification)
        logger.info("Successfully connected to Exchange server!")

        # Test basic connection by getting account info
        logger.info(f"Account primary SMTP address: {account.primary_smtp_address}")
        logger.info(f"Account domain: {account.domain}")

        # Try to get basic folder information with proper error handling
        try:
            logger.info("Testing folder access...")
            inbox_exists = account.inbox is not None
            logger.info(f"Inbox folder accessible: {inbox_exists}")

            if inbox_exists:
                logger.info("Connection test successful!")

                # Try to read emails with retry logic
                logger.info("Attempting to read emails...")
                success = read_emails_with_retry(account, folder_name='inbox', limit=3, max_retries=2)

                if success:
                    logger.info("Email reading completed successfully")
                else:
                    logger.warning("Email reading failed after retries")
            else:
                logger.warning("Inbox folder not accessible")

        except (RateLimitError, ErrorServerBusy) as e:
            logger.warning(f"Server limiting access: {e}")
            logger.info("This is normal for corporate Exchange servers. The connection is working.")

        except Exception as folder_error:
            logger.error(f"Folder access failed: {folder_error}")
            logger.info("The connection is established, but folder access may be restricted.")

    except UnauthorizedError as e:
        logger.error(f"Authentication failed: {e}")
        logger.error("Please verify your credentials are correct")
        raise

    except TransportError as e:
        logger.error(f"Network/Transport error: {e}")
        logger.error("Please check network connectivity and firewall settings")
        raise

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.error("Please check the log file for more details")
        raise

if __name__ == "__main__":
    main()
