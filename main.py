from exchangelib import Credentials, Account, DELEGATE, Message, Mailbox, Folder, Configuration
import datetime
import os
from dotenv import load_dotenv

def configure_account(username, password, email):
    credentials = Credentials(username=username, password=password)
    config = Configuration(server='outlook.office365.com', credentials=credentials)
    account = Account(primary_smtp_address=email, config=config, autodiscover=False, access_type=DELEGATE)
    return account

def read_emails(account, folder_name='inbox', limit=10):
    folder = getattr(account, folder_name)
    for item in folder.all().order_by('-datetime_received')[:limit]:
        print(f"Subject: {item.subject}")
        print(f"Sender: {item.sender}")
        print(f"Body: {item.body}")
        print(f"Received: {item.datetime_received}")
        print("-" * 80)

def move_email(account, subject, new_folder_name):
    email_to_move = account.inbox.filter(subject=subject)[0]
    new_folder = getattr(account, new_folder_name)
    email_to_move.move(to_folder=new_folder)

def delete_email(account, subject):
    email_to_delete = account.inbox.filter(subject=subject)[0]
    email_to_delete.delete()

def filter_emails(account, folder_name='inbox', search_term=''):
    folder = getattr(account, folder_name)
    filtered_emails = folder.filter(subject__contains=search_term)
    for email in filtered_emails:
        print(f"Subject: {email.subject}")
        print(f"Sender: {email.sender}")
        print(f"Body: {email.body}")
        print(f"Received: {email.datetime_received}")
        print("-" * 80)

def create_folder(account, folder_name, parent_folder_name='root'):
    parent_folder = getattr(account, parent_folder_name)
    new_folder = Folder(parent=parent_folder, name=folder_name)
    new_folder.save()

def rename_folder(account, old_folder_name, new_folder_name):
    folder = getattr(account, old_folder_name)
    folder.name = new_folder_name
    folder.save()

def delete_folder(account, folder_name):
    folder = getattr(account, folder_name)
    folder.delete()

def send_email(account, subject, body, to_recipients):
    new_email = Message(
        account=account,
        subject=subject,
        body=body,
        to_recipients=[Mailbox(email_address=recipient) for recipient in to_recipients]
    )
    new_email.send()

def main():
    # Load environment variables from .env file
    load_dotenv()

    # Get credentials from environment variables
    username = os.getenv('USERNAME')
    password = os.getenv('PASSWORD')
    email = os.getenv('EMAIL')

    # Check if all required environment variables are set
    if not all([username, password, email]):
        missing_vars = []
        if not username:
            missing_vars.append('USERNAME')
        if not password:
            missing_vars.append('PASSWORD')
        if not email:
            missing_vars.append('EMAIL')

        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}. Please check your .env file.")

    account = configure_account(username, password, email)

    # Example usage of functions
    read_emails(account, folder_name='inbox', limit=5)
    # move_email(account, subject='Your Subject', new_folder_name='sent')
    # delete_email(account, subject='Your Subject')
    # filter_emails(account, folder_name='inbox', search_term='Important')
    # create_folder(account, folder_name='MyFolder')
    # rename_folder(account, old_folder_name='MyFolder', new_folder_name='RenamedFolder')
    # delete_folder(account, folder_name='RenamedFolder')
    # send_email(account, subject='Test Email', body='This is a test email.', to_recipients=['<EMAIL>'])

if __name__ == "__main__":
    main()
