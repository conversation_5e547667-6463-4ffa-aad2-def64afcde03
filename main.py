from exchangelib import Credentials, Account, DELEGATE, Message, Mailbox, Folder, Configuration, NTLM, FaultTolerance
from exchangelib.protocol import BaseProtocol, NoVerifyHTTPAdapter
from exchangelib.errors import UnauthorizedError, TransportError, RateLimitError, ErrorServerBusy
import os
import logging
import time
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('exchange_client.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def configure_account(username, password, email, disable_ssl_verification=False):
    """Configure Exchange account with direct manual configuration for Telekom environment."""
    logger.info(f"Configuring account for {email}")

    # Disable SSL certificate verification if requested
    if disable_ssl_verification:
        BaseProtocol.HTTP_ADAPTER_CLS = NoVerifyHTTPAdapter
        logger.warning("SSL certificate verification disabled")

    credentials = Credentials(username=username, password=password)

    # Direct manual configuration (autodiscovery always fails for Telekom)
    logger.info("Using direct manual configuration for Telekom Exchange server...")
    config = Configuration(
        server='owa-intern.telekom.de',
        credentials=credentials,
        auth_type=NTLM,
        retry_policy=FaultTolerance(max_wait=120),  # Increased timeout for rate limiting
        max_connections=3  # Connection pooling
    )
    account = Account(
        primary_smtp_address=email,
        config=config,
        autodiscover=False,
        access_type=DELEGATE
    )
    logger.info("Manual configuration successful")
    return account

def read_unread_emails(account, folder_name='inbox', limit=20, max_retries=3):
    """Read unread emails with improved retry logic and error handling."""
    logger.info(f"Reading unread emails from {folder_name} (limit: {limit})")

    for attempt in range(max_retries):
        try:
            folder = getattr(account, folder_name)

            # Filter for unread emails only
            unread_emails = folder.filter(is_read=False).order_by('-datetime_received')[:limit]

            unread_list = list(unread_emails)
            logger.info(f"Found {len(unread_list)} unread emails")

            if len(unread_list) == 0:
                print("📬 No unread emails found!")
                return True

            print(f"\n📧 UNREAD EMAILS ({len(unread_list)} found)")
            print("=" * 60)

            for i, item in enumerate(unread_list, 1):
                logger.info(f"Processing unread email {i}/{len(unread_list)}")
                print(f"\n📩 Email #{i}")
                print(f"📧 Subject: {item.subject}")
                print(f"👤 From: {item.sender}")
                print(f"📅 Received: {item.datetime_received}")
                print(f"🔗 Message ID: {item.message_id}")

                # Show a preview of the body if available
                if hasattr(item, 'body') and item.body:
                    body_preview = str(item.body)[:200].replace('\n', ' ').replace('\r', ' ')
                    print(f"📝 Preview: {body_preview}...")

                print("-" * 50)

            print(f"\n📊 Summary: {len(unread_list)} unread emails displayed")
            return True

        except RateLimitError as e:
            logger.warning(f"Rate limit hit on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                wait_time = min(60, 10 * (2 ** attempt))  # Exponential backoff, max 60s
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                logger.error("Max retries reached due to rate limiting")
                raise

        except ErrorServerBusy as e:
            logger.warning(f"Server busy on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                wait_time = min(30, 5 * (2 ** attempt))  # Shorter backoff for server busy
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
            else:
                logger.error("Max retries reached due to server being busy")
                raise

        except Exception as e:
            logger.error(f"Unexpected error on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                logger.info("Retrying in 5 seconds...")
                time.sleep(5)
            else:
                logger.error("Max retries reached")
                raise

    return False

def list_basic_folders(account):
    """List basic folders/directories in the Exchange account (server-friendly version)."""
    logger.info("Listing basic folders in the Exchange account...")

    try:
        print("\n" + "="*60)
        print("📂 EXCHANGE FOLDER STRUCTURE")
        print("="*60)

        # Try to access common folders without detailed info to be gentle on server
        common_folders = [
            ('inbox', 'Inbox'),
            ('sent', 'Sent Items'),
            ('drafts', 'Drafts'),
            ('deleted_items', 'Deleted Items'),
            ('outbox', 'Outbox'),
            ('calendar', 'Calendar'),
            ('contacts', 'Contacts'),
            ('tasks', 'Tasks'),
            ('notes', 'Notes'),
            ('junk', 'Junk Email')
        ]

        accessible_folders = []
        for attr_name, display_name in common_folders:
            try:
                folder = getattr(account, attr_name, None)
                if folder:
                    accessible_folders.append((attr_name, display_name))
                    print(f"✅ 📁 {display_name}")
                    logger.info(f"Found folder: {display_name}")
                else:
                    print(f"❌ 📁 {display_name} (not accessible)")
            except Exception as e:
                print(f"❌ 📁 {display_name} (error: {str(e)[:50]}...)")
                logger.debug(f"Could not access {display_name}: {e}")

        print(f"\n📊 Summary: {len(accessible_folders)} folders accessible out of {len(common_folders)} checked")
        logger.info(f"Successfully found {len(accessible_folders)} accessible folders")
        return accessible_folders

    except Exception as e:
        logger.error(f"Error listing folders: {e}")
        return []

def move_email(account, subject, new_folder_name):
    email_to_move = account.inbox.filter(subject=subject)[0]
    new_folder = getattr(account, new_folder_name)
    email_to_move.move(to_folder=new_folder)

def delete_email(account, subject):
    email_to_delete = account.inbox.filter(subject=subject)[0]
    email_to_delete.delete()

def filter_emails(account, folder_name='inbox', search_term=''):
    folder = getattr(account, folder_name)
    filtered_emails = folder.filter(subject__contains=search_term)
    for email in filtered_emails:
        print(f"Subject: {email.subject}")
        print(f"Sender: {email.sender}")
        print(f"Body: {email.body}")
        print(f"Received: {email.datetime_received}")
        print("-" * 80)

def create_folder(account, folder_name, parent_folder_name='root'):
    parent_folder = getattr(account, parent_folder_name)
    new_folder = Folder(parent=parent_folder, name=folder_name)
    new_folder.save()

def rename_folder(account, old_folder_name, new_folder_name):
    folder = getattr(account, old_folder_name)
    folder.name = new_folder_name
    folder.save()

def delete_folder(account, folder_name):
    folder = getattr(account, folder_name)
    folder.delete()

def send_email(account, subject, body, to_recipients):
    new_email = Message(
        account=account,
        subject=subject,
        body=body,
        to_recipients=[Mailbox(email_address=recipient) for recipient in to_recipients]
    )
    new_email.send()

def main():
    """Main function with improved error handling and logging."""
    # Load environment variables from .env file with explicit path and override
    env_path = os.path.join(os.getcwd(), '.env')
    load_dotenv(dotenv_path=env_path, override=True)

    # Get credentials from environment variables
    username = os.getenv('USERNAME')
    password = os.getenv('PASSWORD')
    email = os.getenv('EMAIL')
    # Get SSL verification setting (defaults to true for corporate environments)
    disable_ssl_verification = os.getenv('DISABLE_SSL_VERIFICATION', 'true').lower() in ('true', '1', 'yes', 'on')
    # Get folder listing setting (defaults to false to avoid rate limiting)
    list_folders = os.getenv('LIST_FOLDERS', 'false').lower() in ('true', '1', 'yes', 'on')

    # Check if all required environment variables are set
    if not all([username, password, email]):
        missing_vars = []
        if not username:
            missing_vars.append('USERNAME')
        if not password:
            missing_vars.append('PASSWORD')
        if not email:
            missing_vars.append('EMAIL')

        error_msg = f"Missing required environment variables: {', '.join(missing_vars)}. Please check your .env file."
        logger.error(error_msg)
        raise ValueError(error_msg)

    logger.info(f"Starting Exchange client for {email}")
    logger.info(f"SSL verification disabled: {disable_ssl_verification}")

    try:
        account = configure_account(username, password, email, disable_ssl_verification)
        logger.info("Successfully connected to Exchange server!")

        # Test basic connection by getting account info
        logger.info(f"Account primary SMTP address: {account.primary_smtp_address}")
        logger.info(f"Account domain: {account.domain}")

        # Try to get basic folder information with proper error handling
        try:
            logger.info("Testing folder access...")
            inbox_exists = account.inbox is not None
            logger.info(f"Inbox folder accessible: {inbox_exists}")

            if inbox_exists:
                logger.info("Connection test successful!")

                # List basic folders/directories if enabled
                if list_folders:
                    logger.info("Listing available folders...")
                    try:
                        folders = list_basic_folders(account)
                        logger.info(f"Found {len(folders)} accessible folders")
                    except Exception as folder_list_error:
                        logger.warning(f"Could not list folders: {folder_list_error}")
                        logger.info("Continuing with email reading...")
                else:
                    logger.info("Folder listing disabled (LIST_FOLDERS=false). Use list_folders.py for detailed folder listing.")

                # Read unread emails
                logger.info("\nReading unread emails from inbox...")
                success = read_unread_emails(account, folder_name='inbox', limit=10, max_retries=2)

                if success:
                    logger.info("Unread email reading completed successfully")
                else:
                    logger.warning("Unread email reading failed after retries")
            else:
                logger.warning("Inbox folder not accessible")

        except (RateLimitError, ErrorServerBusy) as e:
            logger.warning(f"Server limiting access: {e}")
            logger.info("This is normal for corporate Exchange servers. The connection is working.")

        except Exception as folder_error:
            logger.error(f"Folder access failed: {folder_error}")
            logger.info("The connection is established, but folder access may be restricted.")

    except UnauthorizedError as e:
        logger.error(f"Authentication failed: {e}")
        logger.error("Please verify your credentials are correct")
        raise

    except TransportError as e:
        logger.error(f"Network/Transport error: {e}")
        logger.error("Please check network connectivity and firewall settings")
        raise

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.error("Please check the log file for more details")
        raise

if __name__ == "__main__":
    main()
