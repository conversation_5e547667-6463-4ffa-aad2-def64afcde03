from exchangelib import Credentials, Account, DELEGATE, Message, Mailbox, Folder, Configuration, NTLM, FaultTolerance
from exchangelib.protocol import BaseProtocol, NoVerifyHTTPAdapter
import datetime
import os
from dotenv import load_dotenv

def configure_account(username, password, email, disable_ssl_verification=False):
    # Disable SSL certificate verification if requested
    if disable_ssl_verification:
        BaseProtocol.HTTP_ADAPTER_CLS = NoVerifyHTTPAdapter

    credentials = Credentials(username=username, password=password)

    # Try autodiscovery first, which should work better with corporate environments
    try:
        account = Account(
            primary_smtp_address=email,
            credentials=credentials,
            autodiscover=True,
            access_type=DELEGATE
        )
        return account
    except Exception as e:
        print(f"Autodiscovery failed: {e}")
        print("Trying manual configuration...")

        # Fallback to manual configuration with explicit auth type
        config = Configuration(
            server='owa-intern.telekom.de',
            credentials=credentials,
            auth_type=NTLM,
            retry_policy=FaultTolerance(max_wait=60)  # Add fault tolerance
        )
        account = Account(
            primary_smtp_address=email,
            config=config,
            autodiscover=False,
            access_type=DELEGATE
        )
        return account

def read_emails(account, folder_name='inbox', limit=10):
    folder = getattr(account, folder_name)
    for item in folder.all().order_by('-datetime_received')[:limit]:
        print(f"Subject: {item.subject}")
        print(f"Sender: {item.sender}")
        print(f"Body: {item.body}")
        print(f"Received: {item.datetime_received}")
        print("-" * 80)

def move_email(account, subject, new_folder_name):
    email_to_move = account.inbox.filter(subject=subject)[0]
    new_folder = getattr(account, new_folder_name)
    email_to_move.move(to_folder=new_folder)

def delete_email(account, subject):
    email_to_delete = account.inbox.filter(subject=subject)[0]
    email_to_delete.delete()

def filter_emails(account, folder_name='inbox', search_term=''):
    folder = getattr(account, folder_name)
    filtered_emails = folder.filter(subject__contains=search_term)
    for email in filtered_emails:
        print(f"Subject: {email.subject}")
        print(f"Sender: {email.sender}")
        print(f"Body: {email.body}")
        print(f"Received: {email.datetime_received}")
        print("-" * 80)

def create_folder(account, folder_name, parent_folder_name='root'):
    parent_folder = getattr(account, parent_folder_name)
    new_folder = Folder(parent=parent_folder, name=folder_name)
    new_folder.save()

def rename_folder(account, old_folder_name, new_folder_name):
    folder = getattr(account, old_folder_name)
    folder.name = new_folder_name
    folder.save()

def delete_folder(account, folder_name):
    folder = getattr(account, folder_name)
    folder.delete()

def send_email(account, subject, body, to_recipients):
    new_email = Message(
        account=account,
        subject=subject,
        body=body,
        to_recipients=[Mailbox(email_address=recipient) for recipient in to_recipients]
    )
    new_email.send()

def main():
    # Load environment variables from .env file
    load_dotenv()

    # Get credentials from environment variables
    username = os.getenv('USERNAME')
    password = os.getenv('PASSWORD')
    email = os.getenv('EMAIL')
    # Get SSL verification setting (defaults to False for corporate environments)
    disable_ssl_verification = os.getenv('DISABLE_SSL_VERIFICATION', 'true').lower() in ('true', '1', 'yes', 'on')

    # Check if all required environment variables are set
    if not all([username, password, email]):
        missing_vars = []
        if not username:
            missing_vars.append('USERNAME')
        if not password:
            missing_vars.append('PASSWORD')
        if not email:
            missing_vars.append('EMAIL')

        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}. Please check your .env file.")

    print(f"Connecting to Exchange server for {email}...")
    print(f"SSL verification disabled: {disable_ssl_verification}")

    try:
        account = configure_account(username, password, email, disable_ssl_verification)
        print("Successfully connected to Exchange server!")

        # Test basic connection by getting account info
        print(f"Account primary SMTP address: {account.primary_smtp_address}")
        print(f"Account domain: {account.domain}")

        # Try to get basic folder information (this is less intensive than reading emails)
        try:
            print("Testing folder access...")
            print(f"Inbox folder exists: {account.inbox is not None}")
            print("Connection test successful!")

            # Only try to read emails if basic connection works
            print("\nAttempting to read a few emails...")
            read_emails(account, folder_name='inbox', limit=2)  # Reduced limit to be gentler on the server

        except Exception as folder_error:
            print(f"Folder access failed: {folder_error}")
            print("This might be due to server rate limiting or permissions.")
            print("The connection itself is working, but folder access is restricted.")

    except Exception as e:
        print(f"Failed to connect to Exchange server: {e}")
        print("\nTroubleshooting tips:")
        print("1. Verify your credentials are correct")
        print("2. Check if your account has access to Exchange Web Services")
        print("3. Verify network connectivity to the Exchange server")
        print("4. Check if there are any firewall restrictions")
        print("5. Try using Outlook Web Access to verify your credentials work")
        raise
    # move_email(account, subject='Your Subject', new_folder_name='sent')
    # delete_email(account, subject='Your Subject')
    # filter_emails(account, folder_name='inbox', search_term='Important')
    # create_folder(account, folder_name='MyFolder')
    # rename_folder(account, old_folder_name='MyFolder', new_folder_name='RenamedFolder')
    # delete_folder(account, folder_name='RenamedFolder')
    # send_email(account, subject='Test Email', body='This is a test email.', to_recipients=['<EMAIL>'])

if __name__ == "__main__":
    main()
