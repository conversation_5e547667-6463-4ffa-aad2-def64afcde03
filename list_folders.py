#!/usr/bin/env python3
"""
Standalone script to list Exchange folders/directories.
Run this separately when the server is less busy.
"""

from exchangelib import Credentials, Account, DELEGATE, Configuration, NTLM, FaultTolerance
from exchangelib.protocol import BaseProtocol, NoVerifyHTTPAdapter
import os
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def configure_account():
    """Configure Exchange account."""
    # Load environment variables
    env_path = os.path.join(os.getcwd(), '.env')
    load_dotenv(dotenv_path=env_path, override=True)
    
    username = os.getenv('USERNAME')
    password = os.getenv('PASSWORD')
    email = os.getenv('EMAIL')
    disable_ssl_verification = os.getenv('DISABLE_SSL_VERIFICATION', 'true').lower() in ('true', '1', 'yes', 'on')
    
    if not all([username, password, email]):
        raise ValueError("Missing required environment variables. Check your .env file.")
    
    logger.info(f"Configuring account for {email}")
    
    # Disable SSL certificate verification if requested
    if disable_ssl_verification:
        BaseProtocol.HTTP_ADAPTER_CLS = NoVerifyHTTPAdapter
        logger.warning("SSL certificate verification disabled")
    
    credentials = Credentials(username=username, password=password)
    
    # Direct manual configuration
    config = Configuration(
        server='owa-intern.telekom.de',
        credentials=credentials,
        auth_type=NTLM,
        retry_policy=FaultTolerance(max_wait=180),  # Longer timeout for folder listing
        max_connections=1  # Single connection to be gentle
    )
    account = Account(
        primary_smtp_address=email,
        config=config,
        autodiscover=False,
        access_type=DELEGATE
    )
    logger.info("Account configured successfully")
    return account

def list_folders_detailed(account):
    """List folders with more details when server allows."""
    logger.info("Listing folders with details...")
    
    print("\n" + "="*70)
    print("📂 DETAILED EXCHANGE FOLDER STRUCTURE")
    print("="*70)
    
    # Common folders to check
    folders_to_check = [
        ('inbox', 'Inbox'),
        ('sent', 'Sent Items'),
        ('drafts', 'Drafts'),
        ('deleted_items', 'Deleted Items'),
        ('outbox', 'Outbox'),
        ('calendar', 'Calendar'),
        ('contacts', 'Contacts'),
        ('tasks', 'Tasks'),
        ('notes', 'Notes'),
        ('junk', 'Junk Email')
    ]
    
    accessible_folders = []
    
    for attr_name, display_name in folders_to_check:
        try:
            logger.info(f"Checking folder: {display_name}")
            folder = getattr(account, attr_name, None)
            
            if folder:
                accessible_folders.append((attr_name, display_name, folder))
                print(f"✅ 📁 {display_name}")
                
                # Try to get folder statistics (this might trigger rate limiting)
                try:
                    total_count = getattr(folder, 'total_count', None)
                    unread_count = getattr(folder, 'unread_count', None)
                    
                    if total_count is not None:
                        print(f"   📧 Total items: {total_count}")
                    if unread_count is not None:
                        print(f"   📬 Unread items: {unread_count}")
                        
                except Exception as stats_error:
                    print(f"   ⚠️  Could not get statistics: {str(stats_error)[:50]}...")
                    logger.debug(f"Statistics error for {display_name}: {stats_error}")
                
                print()  # Empty line for readability
                
            else:
                print(f"❌ 📁 {display_name} (not accessible)")
                
        except Exception as e:
            print(f"❌ 📁 {display_name} (error: {str(e)[:50]}...)")
            logger.warning(f"Could not access {display_name}: {e}")
    
    print(f"📊 Summary: {len(accessible_folders)} folders accessible out of {len(folders_to_check)} checked")
    return accessible_folders

def main():
    """Main function."""
    try:
        print("🔗 Connecting to Exchange server...")
        account = configure_account()
        print("✅ Connected successfully!")
        
        print(f"📧 Account: {account.primary_smtp_address}")
        print(f"🏢 Domain: {account.domain}")
        
        # List folders
        folders = list_folders_detailed(account)
        
        print(f"\n🎉 Folder listing completed! Found {len(folders)} accessible folders.")
        
    except Exception as e:
        logger.error(f"Error: {e}")
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
